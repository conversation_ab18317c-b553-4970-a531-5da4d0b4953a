import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import 'package:intl/intl.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_profile_widget.dart';
import '../../../../utillites/common_shimmer_profile.dart';
import '../../../../utillites/custom_scroll.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../bottom_bar/views/bottom_bar_view.dart';
import '../../profile_view/components/profile_widget_view.dart';
import '../../user_detail/components/image_picker_bottom_sheet.dart';
import '../components/project_detail_about_view.dart';
import '../components/project_detail_header.dart';
import '../controllers/project_detail_view_controller.dart';

class ProjectDetailView extends StatelessWidget {
  const ProjectDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return GetBuilder<ProjectDetailViewController>(
        init: ProjectDetailViewController(),
        tag: Get.arguments != null ? Get.arguments['projectId']?.toString() : null,
      builder: (controller) {
        return Scaffold(
          bottomNavigationBar: customBottomNavigation(),
          floatingActionButton: Obx(
            () => controller.apiManager.isLoading
                ? SizedBox()
                : (CurrentUser.user.id ==
                            controller.getProjectDetailData.value.userId ||
                        (controller.getProjectDetailData.value.projectMembers
                                .isNotEmpty &&
                            controller.getProjectDetailData.value.projectMembers[0]
                                    .access ==
                                "write"))
                    ? controller.sections[controller.currentSelectedIndex.value]
                        .floatingActionButtonBuilder(context)
                    : SizedBox(),
          ),
          body: Obx(
            () => controller.isUserLoading.value
                ? ShimmerProfilePage(
                isProjectDetail: true,
                              )
                : controller.isProjectNotFound.value
                    ? Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(30)),
                        child: SafeArea(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              buildIcon(AppImage.backArrow, () {
                                print("route == ${Get.previousRoute}");
                                Get.back();
                              }, padding: 7,color:
                                AppTheme.whiteWithBase,),
                              // Space.height(200),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Center(
                                    child: Image.asset(
                                      "assets/images/not_folder.png",
                                      width: MySize.getScaledSizeWidth(200),
                                      height: MySize.getScaledSizeHeight(200),
                                    ),
                                  ),
                                  Space.height(20),
                                  Center(
                                    child: TypoGraphy(
                                      text: "Project not found!",
                                      level: 4,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox.shrink(),
                            ],
                          ),
                        ),
                      )
                    : Obx(
                      () => CustomScrollView(
                        physics: ClampingScrollPhysics(),
                        slivers: [
                          SliverToBoxAdapter(
                            child: Stack(
                              children: [
                                buildProjectImage(
                                    imageUrl: controller
                                        .getProjectDetailData.value.image, defaultImageUrl: AppImage.defaultBannerImage),
                                buildTopGradient(),
                                buildTopBar(context,
                                    controller: controller),
                              ],
                            ),
                          ),
                          SliverToBoxAdapter(child: Space.height(20)),
                          SliverToBoxAdapter(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySize.getScaledSizeWidth(30)),
                                  child: InkWell(
                                    onTap: () {
                                      ImagePickerBottomSheet.show(
                                        context: context,
                                        child: ProjectDetailsWidget(
                                          projectName: controller
                                              .getProjectDetailData
                                              .value
                                              .name,
                                          projectDescription: controller
                                              .getProjectDetailData
                                              .value
                                              .description,
                                          userImage: controller
                                                  .getProjectDetailData
                                                  .value
                                                  .userData
                                                  ?.image ??
                                              "",
                                          userName: controller
                                                  .getProjectDetailData
                                                  .value
                                                  .userData
                                                  ?.firstName ??
                                              "",
                                          userLastName: controller
                                                  .getProjectDetailData
                                                  .value
                                                  .userData
                                                  ?.lastName ??
                                              "",
                                          createdAt: DateFormat('MMM d, y')
                                              .format(DateTime.parse(
                                                  controller
                                                      .getProjectDetailData
                                                      .value
                                                      .createdAt
                                                      .toString())),
                                          isCurrentUser:
                                              CurrentUser.user.id ==
                                                  controller
                                                      .getProjectDetailData
                                                      .value
                                                      .userData
                                                      ?.id,
                                          currentUseId: controller
                                                  .getProjectDetailData
                                                  .value
                                                  .userData
                                                  ?.id ??
                                              0,
                                        ),
                                      );
                                    },
                                    child: RichText(
                                      // textAlign: TextAlign.start,
                                      text: TextSpan(
                                        children: [
                                          if (controller
                                              .getProjectDetailData
                                              .value
                                              .isPrivate) ...[
                                            WidgetSpan(
                                                child: Padding(
                                              padding: EdgeInsets.only(
                                                  right: MySize
                                                      .getScaledSizeWidth(
                                                          8)),
                                              child: buildPrivateLockIcon(
                                                  height: 32),
                                            )),
                                          ],
                                          TextSpan(
                                            text: controller
                                                .getProjectDetailData
                                                .value
                                                .name,
                                            style: TextStyle(
                                              fontSize: 30,
                                              fontWeight: FontWeight.w700,
                                              color: AppTheme.whiteWithBase,
                                            ),
                                          ),
                                          WidgetSpan(
                                            // alignment: PlaceholderAlignment.middle,
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                  left: 10, bottom: 5),
                                              child: SvgPicture.asset(
                                                AppImage.back,
                                                height: MySize.size16,
                                                color:
                                                    AppTheme.whiteWithNull,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                Space.height(8),
                                if (controller.getProjectDetailData.value
                                        .location !=
                                    null) ...[
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(
                                              top: MySize.size2 ?? 10),
                                          child: Image.asset(
                                            AppImage.location,
                                            height:
                                                MySize.getScaledSizeHeight(
                                                    15),
                                            width:
                                                MySize.getScaledSizeWidth(
                                                    15),
                                          ),
                                        ),
                                        Space.width(4),
                                        Expanded(
                                          child: TypoGraphy(
                                            text: controller
                                                .getProjectDetailData
                                                .value
                                                .location,
                                            level: 3,
                                            color: AppTheme.grey,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Space.height(8),
                                ],
                                if (CurrentUser.user.id ==
                                    controller.getProjectDetailData.value
                                        .userData?.id) ...[
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: TypoGraphy(
                                      text: "Created by me",
                                      level: 2,
                                      color: AppTheme.grey,
                                      fontWeight: FontWeight.w400,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                ],
                                if (CurrentUser.user.id !=
                                    controller.getProjectDetailData.value
                                        .userData?.id) ...[
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: MySize.size30 ?? 30),
                                    child: InkWell(
                                      onTap: () {
                                        Get.toNamed(
                                            Routes.other_user_profile,
                                            arguments: {
                                              "UserId": controller
                                                  .getProjectDetailData
                                                  .value
                                                  .userData
                                                  ?.id
                                            });
                                      },
                                      child: Row(
                                        children: [
                                          TypoGraphy(
                                            text: "Created by:",
                                            level: 2,
                                            color: AppTheme.grey,
                                            fontWeight: FontWeight.w400,
                                            fontStyle: FontStyle.italic,
                                          ),
                                          Space.width(5),
                                          profileImage(
                                            url: controller
                                                    .getProjectDetailData
                                                    .value
                                                    .userData
                                                    ?.image ??
                                                "",
                                            userName: controller
                                                    .getProjectDetailData
                                                    .value
                                                    .userData
                                                    ?.firstName ??
                                                "",
                                            iconHeight:
                                                MySize.getScaledSizeHeight(
                                                    16),
                                            iconWidth:
                                                MySize.getScaledSizeHeight(
                                                    16),
                                            height:
                                                MySize.getScaledSizeHeight(
                                                    16),
                                            width:
                                                MySize.getScaledSizeHeight(
                                                    16),
                                            strokeWidth:
                                                MySize.size1 ?? 1.0,
                                            borderColor: Colors.transparent,
                                            textStyle: TextStyle(
                                                fontSize: MySize.size10,
                                                color: AppTheme.white),
                                          ),
                                          Space.width(5),
                                          TypoGraphy(
                                            text:
                                                "${controller.getProjectDetailData.value.userData?.firstName} ${controller.getProjectDetailData.value.userData?.lastName}",
                                            level: 2,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                                Space.height(20),
                                if (CurrentUser.user.id !=
                                        controller.getProjectDetailData
                                            .value.userData?.id &&
                                    controller.getProjectDetailData.value
                                        .projectMembers.isEmpty) ...[
                                  Space.height(4),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.center,
                                    children: [
                                      if (!controller.isProjectFollow.value)
                                        Buttons(
                                          buttonText: "Follow",
                                          buttonTextLevel: 4,
                                          width: MySize.getScaledSizeWidth(
                                              125),
                                          height:
                                              MySize.getScaledSizeHeight(
                                                  64),
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            controller
                                                .callApiForProjectFollow(
                                                    context: context,
                                                    projectId: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .id
                                                        .toString());
                                          },
                                        ),
                                      if (controller.isProjectFollow.value)
                                        InkWell(
                                          onTap: () {
                                            HapticFeedback.lightImpact();
                                            controller
                                                .callApiForProjectUnFollow(
                                                    context: context,
                                                    projectId: controller
                                                        .getProjectDetailData
                                                        .value
                                                        .id
                                                        .toString());
                                          },
                                          child: Container(
                                            width:
                                                MySize.getScaledSizeWidth(
                                                    125),
                                            height:
                                                MySize.getScaledSizeHeight(
                                                    64),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        20),
                                                border: Border.all(
                                                    width: 1,
                                                    color: AppTheme.grey)),
                                            alignment: Alignment.center,
                                            child: TypoGraphy(
                                              text: "Following",
                                              textStyle: TextStyle(
                                                  fontSize: 15,
                                                  fontWeight:
                                                      FontWeight.w600,
                                                  fontFamily: "Inter",
                                                  color: AppTheme.grey),
                                            ),
                                          ),
                                        )
                                    ],
                                  ),
                                ],
                                Space.height(12),
                                buildStoryHighlightSection(
                                    context: context,
                                    highlightList: controller.highlights,
                                    fontSize: MySize.size13 ?? 13,
                                    projectId: controller.projectId.value,
                                    userId: controller
                                        .getProjectDetailData.value.userId,
                                    isLoading: controller
                                        .isHighlightLoading.value),
                                Space.height(30),
                                Builder(builder: (context) {
                                  final labelList =
                                      buildPostCategorySection(
                                    isProjectMembers: controller
                                        .getProjectDetailData
                                        .value
                                        .projectMembers
                                        .isNotEmpty,
                                    userPostList: controller.sections
                                        .map((e) => e.title)
                                        .toList(),
                                    currentSelectedIndex: controller
                                        .currentSelectedIndex.value,
                                    onCategorySelected: (index) {
                                      controller.currentSelectedIndex
                                          .value = index;
                                      controller.sections[index]
                                          .onCategorySelected();
                                    },
                                  );
                                  return controller.sections.length >= 4
                                      ? CustomHorizontalScrollbar(
                                          scrollController:
                                              controller.scrollController,
                                          child: labelList,
                                        )
                                      : labelList;
                                })
                              ],
                            ),
                          ),
                          ...controller.sections[
                                  controller.currentSelectedIndex.value]
                              .viewBuilder(context)
                        ],
                      ),
                    ),
          ),
        );
      }
    );
  }
}
