import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../constants/app_image.dart';
import '../../../../constants/app_size_constant.dart';
import '../../../../utillites/app_text_field.dart';
import '../../../../utillites/app_theme.dart';
import '../../../../utillites/buttons.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';
import '../../../../utillites/loader.dart';
import '../../../../utillites/locaion_input_field.dart';
import '../../../../utillites/network_image.dart';
import '../../../../utillites/typography.dart';
import '../../../routes/app_pages.dart';
import '../../post/components/image_select_dialog.dart';
import '../../user_detail/components/profile_image_view.dart';
import '../controllers/create_project_controller.dart';
import 'create_sub_project.dart';

class CreateProjectView extends GetWidget<CreateProjectController> {
  const CreateProjectView({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      // backgroundColor: AppTheme.white,
      body: Stack(
        children: [
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (controller.arg != null &&
                        controller.arg["isFromSignUp"] != null)
                    ? Space.height(10)
                    : Space.height(5),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: MySize.size45 ?? 25),
                  child: Row(
                    children: [
                      InkWell(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          if (controller.arg != null &&
                              controller.arg['isBottom'] != null) {
                            Get.offAllNamed(Routes.Bottom_Bar,
                                arguments: {"index": 1});
                          } else {
                            Get.back();
                          }
                        },
                        child: SvgPicture.asset(
                          AppImage.backArrow,
                          height: MySize.size28,
                          width: MySize.size28,
                          color: AppTheme.whiteWithNull,
                        ),
                      ),
                      Spacer(),
                      if (controller.arg != null &&
                          controller.arg["isFromSignUp"] != null)
                        InkWell(
                          onTap: () {
                            CurrentUser.getMe(
                              callback: () async {
                                Get.offAllNamed(Routes.Bottom_Bar,
                                    arguments: {"isFirstTime": true});
                              },
                            );
                          },
                          child: TypoGraphy(
                            text: "Skip",
                            level: 4,
                            color: AppTheme.whiteWithNull,
                          ),
                        ),
                    ],
                  ),
                ),
                Space.height(10),
                Align(
                  alignment: Alignment.center,
                  child: TypoGraphy(
                    text: (controller.arg != null &&
                            controller.arg["isEdit"] != null)
                        ? "Edit Project"
                        : "Create Project",
                    level: 8,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(50)),
                    child: TypoGraphy(
                      text: (controller.arg != null &&
                              controller.arg["isFromSignUp"] != null)
                          ? "90% of the new users find this app helpful when they create a new Project"
                          : "Share the project you are working on.",
                      level: 3,
                      fontWeight: FontWeight.w400,
                      color: AppTheme.grey,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                Space.height(10),
                Expanded(
                  child: SingleChildScrollView(
                    keyboardDismissBehavior:
                        ScrollViewKeyboardDismissBehavior.onDrag,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Space.height(20),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.size45 ?? 25),
                          child: AppTextField(
                            focusNode: controller.projectNameFocusNode,
                            controller: controller.projectNameController.value,
                            labelText: "Project Name*",
                            maxLength: 50,

                            onChangedValue: (p0) {
                              controller.projectNameController.refresh();
                              controller.update();
                            },
                            textCapitalization: TextCapitalization.sentences,
                          ),
                        ),
                        Obx(
                          () => (controller.projectNameController.value.text
                                      .length >=
                                  50)
                              ? Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySize.getScaledSizeWidth(45)).copyWith(top: MySize.getScaledSizeWidth(10)),
                                  child: TypoGraphy(
                                    text: "Maximum 50 characters allowed",
                                    level: 2,
                                    fontWeight: FontWeight.w400,
                                    color: AppTheme.red,
                                  ),
                                )
                              : SizedBox.shrink(),
                        ),
                        ExpansionTile(
                          childrenPadding: EdgeInsets.symmetric(
                              horizontal: MySize.size45 ?? 25),
                          iconColor: AppTheme.whiteWithBase,
                          collapsedIconColor: AppTheme.whiteWithBase,
                          tilePadding: EdgeInsets.symmetric(
                              horizontal: MySize.size45 ?? 25),
                          shape: const RoundedRectangleBorder(
                            side: BorderSide.none,
                          ),
                          title: TypoGraphy(
                            text: "Add More (Optional)",
                            level: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          children: [
                            Obx(
                              () => ListView.builder(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: controller.subProjects.isEmpty
                                    ? 1
                                    : controller.subProjects.length,
                                // Check if it's empty
                                itemBuilder: (context, index) {
                                  if (controller.subProjects.isEmpty) {
                                    return AppTextField(
                                      onTap: () {
                                        // if ((controller.arg != null &&
                                        //     controller.arg["isEdit"] != null)) {
                                        //   return;
                                        // }
                                        if (controller.subProjects.isEmpty) {
                                          showModalBottomSheet(
                                            context: context,
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.vertical(
                                                      top: Radius.circular(20)),
                                            ),
                                            builder: (context) =>
                                                subProjectVisibility(
                                                    context,
                                                    (controller.arg != null &&
                                                            controller.arg[
                                                                    "isEdit"] !=
                                                                null)
                                                        ? true
                                                        : false),
                                          );
                                        } else {
                                          if ((controller.arg != null &&
                                              controller.arg["isEdit"] !=
                                                  null)) {
                                            return;
                                          }
                                        }
                                      },
                                      enabled: false,
                                      controller: controller
                                              .subProjectNameControllers.isEmpty
                                          ? TextEditingController() // Show empty controller if list is empty
                                          : controller
                                              .subProjectNameControllers[index],
                                      labelText: "Sub-Project Name(optional)",
                                      maxLength: 30,
                                      textCapitalization:
                                          TextCapitalization.words,
                                      readOnly:
                                          true, // Allow editing when adding a new subproject
                                    );
                                  } else {
                                    return Column(
                                      children: [
                                        AppTextField(
                                          onTap: () {
                                            if ((controller.arg != null &&
                                                controller.arg["isEdit"] !=
                                                    null)) {
                                              return;
                                            }
                                            var subProject =
                                                controller.subProjects[index];

                                            // Set only the selected sub-project's values
                                            // controller.selectedSubProjectIndex.value = index;
                                            controller.subProjectNameController
                                                .value.text = subProject.name;
                                            controller
                                                .subProjectDescriptionController
                                                .value
                                                .text = subProject.description!;
                                            controller.selectedImageSubProject
                                                .value = subProject.image!;
                                            controller.visibilityValueSubProject
                                                    .value =
                                                subProject.isPrivate ? 1 : 0;
                                            showModalBottomSheet(
                                              context: context,
                                              isScrollControlled: true,
                                              backgroundColor:
                                                  Colors.transparent,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.vertical(
                                                        top: Radius.circular(
                                                            20)),
                                              ),
                                              builder: (context) =>
                                                  subProjectVisibility(
                                                      context,
                                                      (controller.arg != null &&
                                                              controller.arg[
                                                                      "isEdit"] !=
                                                                  null)
                                                          ? true
                                                          : false,
                                                      selectedSubProject:
                                                          controller
                                                                  .subProjects[
                                                              index],
                                                      subProjectIndex: index),
                                            );
                                          },
                                          controller: controller
                                              .subProjectNameControllers[index],
                                          labelText:
                                              "Sub-Project Name${index == 0 ? "(optional)" : " ${index + 1}"}",
                                          maxLength: 30,
                                          textCapitalization:
                                              TextCapitalization.words,
                                          readOnly:
                                              true, // Keep it read-only when viewing the subproject
                                        ),
                                        Space.height(10),
                                        if (index != 0 &&
                                            !(controller.arg != null &&
                                                controller.arg["isEdit"] !=
                                                    null))
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: GestureDetector(
                                              child: TypoGraphy(
                                                textStyle: TextStyle(
                                                  fontSize: MySize
                                                      .getScaledSizeHeight(13),
                                                  fontWeight: FontWeight.w400,
                                                  color: AppTheme.red,
                                                ),
                                                text: "Remove",
                                              ),
                                              onTap: () {
                                                controller.subProjects
                                                    .removeAt(index);
                                                controller
                                                    .subProjectNameControllers
                                                    .removeAt(index);
                                              },
                                            ),
                                          ),
                                        Space.height(10),
                                      ],
                                    );
                                  }
                                },
                              ),
                            ),
                            Obx(() =>
                                controller.subProjectNameControllers.isEmpty
                                    ? Space.height(20)
                                    : Space.height(0)),
                            (controller.arg != null &&
                                    controller.arg["isEdit"] != null)
                                ? SizedBox()
                                : InkWell(
                                    onTap: () {
                                      if (controller
                                          .projectNameFocusNode.hasFocus) {
                                        controller.projectNameFocusNode
                                            .unfocus();
                                      }
                                      if (controller.projectDescriptionFocusNode
                                          .value.hasFocus) {
                                        controller
                                            .projectDescriptionFocusNode.value
                                            .unfocus();
                                      }
                                      controller
                                          .subProjectDescriptionController.value
                                          .clear();
                                      controller.subProjectNameController.value
                                          .clear();
                                      controller.selectedImageSubProject.value =
                                          "";
                                      showModalBottomSheet(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(40)),
                                        ),
                                        builder: (context) =>
                                            subProjectVisibility(
                                                context,
                                                (controller.arg != null &&
                                                        controller.arg[
                                                                "isEdit"] !=
                                                            null)
                                                    ? true
                                                    : false),
                                      );
                                    },
                                    child: Align(
                                      alignment: Alignment.centerRight,
                                      child: TypoGraphy(
                                        text: "+ Add More",
                                        level: 4,
                                        color: AppTheme.primaryIconDark,
                                      ),
                                    ),
                                  ),
                            Space.height(30),
                            GetBuilder<CreateProjectController>(
                              builder: (controller) => AppTextField(
                                textInputAction: TextInputAction.newline,
                                textInputType: TextInputType.multiline,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                height: MySize.size140,
                                focusNode: controller
                                    .projectDescriptionFocusNode.value,
                                controller: controller
                                    .projectDescriptionController.value,
                                labelText: controller
                                            .projectDescriptionFocusNode
                                            .value
                                            .hasFocus ||
                                        controller.projectDescriptionController
                                            .value.text.isNotEmpty
                                    ? "Description"
                                    : null,
                                hintText: controller.projectDescriptionFocusNode
                                        .value.hasFocus
                                    ? ""
                                    : "Description",
                                maxLines: 5,
                                maxLength: 250,
                              ),
                            ),
                            Space.height(6),
                            Obx(
                              () => Align(
                                alignment: Alignment.centerRight,
                                child: TypoGraphy(
                                  text:
                                      "${controller.charCount}/${controller.maxChars}",
                                  level: 2,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            Space.height(20),
                            LocationInputField(
                              controller: controller.locationController.value,
                              key: controller.locationFieldKey,
                              selectedSuggestion: controller.meetLocation,
                              onFocused: () {
                                controller.scrollToLocationField(context);
                              },
                            ),
                            Space.height(5),
                            TypoGraphy(
                              text:
                                  "Add a location to find specific project easily.",
                              level: 3,
                              fontWeight: FontWeight.w300,
                              fontStyle: FontStyle.italic,
                              color: AppTheme.grey,
                            ),
                            Space.height(30),
                            Obx(
                              () => GestureDetector(
                                onTap: () {
                                  if (controller
                                      .projectNameFocusNode.hasFocus) {
                                    controller.projectNameFocusNode.unfocus();
                                  }
                                  if (controller.projectDescriptionFocusNode
                                      .value.hasFocus) {
                                    controller.projectDescriptionFocusNode.value
                                        .unfocus();
                                  }
                                  if (controller.selectedImage.value == "") {
                                    // Get.dialog(
                                    //   barrierDismissible: false,
                                    ImageTypeSelectPopup(
                                      onCamera: () async {
                                        // Get.back();
                                        Navigator.pop(context);
                                        File? pick = await CommonFunction
                                            .pickImageFromCamera();

                                        if (pick != null) {
                                          if (await controller
                                              .isValidImage(pick)) {
                                            controller.callApiForProjectImage(
                                                context: context,
                                                imageFile: pick.path);
                                            // controller.selectedImage.value = pick;
                                          } else {
                                            CommonFunction.showCustomSnackbar(
                                                message:
                                                    "Please upload a JPEG or PNG file under 2MB.",
                                                backgroundColor: AppTheme.red,
                                                isError: true);
                                          }
                                        }
                                      },
                                      onGallery: () async {
                                        // Get.back();
                                        Navigator.pop(context);
                                        File? pick = await CommonFunction
                                            .pickImageFromGallery();
                                        if (pick != null) {
                                          if (await controller
                                              .isValidImage(pick)) {
                                            controller.callApiForProjectImage(
                                                context: context,
                                                imageFile: pick.path);
                                            // controller.selectedImage.value = pick;
                                          } else {
                                            CommonFunction.showCustomSnackbar(
                                                message:
                                                    "Please upload a JPEG or PNG file under 2MB.",
                                                backgroundColor: AppTheme.red,
                                                isError: true);
                                          }
                                        }
                                      },
                                    );
                                    // );
                                  }
                                },
                                child: controller.selectedImage.value != ""
                                    ? ProfileImageWithEditIcon(
                                        top: -8,
                                        right: -10,
                                        clipOval: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(14),
                                            child: NetworkImageComponent(
                                              imageUrl: controller
                                                  .selectedImage.value,
                                              simmerHeight:
                                                  MySize.getScaledSizeHeight(
                                                      220),
                                              height: MySize.size220,
                                              width: double.infinity,
                                              // aspectRatio: 430/183,
                                            )),
                                        editIconPath: AppImage.closeImage,
                                        color: AppTheme.baseBlack,
                                        padding: 10,
                                        imageSize: MySize.size120 ?? 120,
                                        iconSize: MySize.size32 ?? 34,
                                        blurSigma: MySize.size35 ?? 30,
                                        onEditIconTap: () {
                                          controller.selectedImage.value = "";
                                        },
                                      )
                                    : Container(
                                        height: MySize.size220,
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(14),
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? AppTheme.darkBackground
                                              : AppTheme.lightGrey,
                                        ),
                                        child: DottedBorder(
                                          borderType: BorderType.RRect,
                                          dashPattern: const [8, 6],
                                          color: AppTheme.grey,
                                          borderPadding:
                                              EdgeInsets.all(MySize.size1 ?? 1),
                                          radius: Radius.circular(14),
                                          child: Center(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(
                                                  AppImage.uploadImage,
                                                  height: MySize.size50,
                                                  width: MySize.size50,
                                                ),
                                                Space.height(12),
                                                TypoGraphy(
                                                  text: "Upload Image",
                                                  level: 12,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                                Space.height(4),
                                                TypoGraphy(
                                                  text: "Formats: JPEG, PNG",
                                                  level: 3,
                                                  fontWeight: FontWeight.w300,
                                                  color: AppTheme.grey,
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                              ),
                            ),
                            Space.height(30),
                          ],
                        ),

                        // Space.height(30),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.size45 ?? 25),
                          child: TypoGraphy(
                            text: "Select Project Visibility*",
                            level: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Space.height(6),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.size45 ?? 25),
                          child: TypoGraphy(
                            text:
                                "Public projects are visible to everyone, while private projects are limited to you and your team. ",
                            level: 3,
                            fontWeight: FontWeight.w400,
                            color: AppTheme.grey,
                          ),
                        ),
                        Obx(
                          () => Padding(
                            padding: EdgeInsets.only(left: MySize.size30 ?? 25),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Transform.scale(
                                  scale: 1.2,
                                  child: Radio(
                                    activeColor: AppTheme.primary1,
                                    value: 0,
                                    onChanged: (value) {
                                      controller.visibilityValue.value = value!;
                                      controller.visibilityName.value =
                                          "Public";
                                      controller
                                              .visibilityValueSubProject.value =
                                          0; // Keep sub-project selectable
                                      controller.update();
                                    },
                                    groupValue:
                                        controller.visibilityValue.value,
                                  ),
                                ),
                                TypoGraphy(
                                  text: "Public",
                                  textStyle: TextStyle(
                                    fontSize: MySize.getScaledSizeHeight(16),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                SizedBox(width: MySize.getScaledSizeWidth(40)),
                                Transform.scale(
                                  scale: 1.2,
                                  child: Radio(
                                    activeColor: AppTheme.primary1,
                                    value: 1,
                                    onChanged: (value) {
                                      controller.visibilityValue.value = value!;
                                      controller.visibilityName.value =
                                          "Private";
                                      controller.visibilityValueSubProject
                                          .value = 1; // Auto-select Private
                                      controller.update();
                                    },
                                    groupValue:
                                        controller.visibilityValue.value,
                                  ),
                                ),
                                TypoGraphy(
                                  text: "Private",
                                  textStyle: TextStyle(
                                    fontSize: MySize.getScaledSizeHeight(16),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Obx(
                          () => (controller.visibilityName.value !=
                                      controller
                                          .originalVisibilityValue.value &&
                                  controller.arg != null &&
                                  controller.arg["isEdit"] != null)
                              ? Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySize.getScaledSizeWidth(45)),
                                  child: TypoGraphy(
                                    text:
                                        "* Are you sure you want to change the visibility to ${controller.visibilityName.value}?  All your content will also be visible to ${controller.visibilityName.value}",
                                    level: 3,
                                    color: AppTheme.red,
                                  ),
                                )
                              : SizedBox(),
                        ),
                        Space.height(20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(
                              () => Buttons(
                                width: MySize.size198,
                                buttonText: controller.arg != null &&
                                        controller.arg["isEdit"] != null
                                    ? "Update"
                                    : "Create",
                                buttonTextLevel: 4,
                                isLoading: controller.isLoading.value,
                                onTap: () {
                                  HapticFeedback.lightImpact();
                                  if (controller.visibilityValue.value != -1 &&
                                      controller
                                          .projectNameController.value.text
                                          .trim()
                                          .isNotEmpty) {
                                    bool isLocationFilled = controller
                                        .locationController.value.text
                                        .trim()
                                        .isNotEmpty;
                                    bool isEditing = controller.arg != null &&
                                        controller.arg["isEdit"] != null;
                                    if (!isLocationFilled ||
                                        controller.meetLocation.value ==
                                            controller.locationController.value
                                                .text) {
                                      if (isEditing) {
                                        controller.callApiForUpdateOneProject(
                                            context: context);
                                      } else {
                                        controller.callApiForCreateProject(
                                            context: context);
                                      }
                                    } else {
                                      CommonFunction.showCustomSnackbar(
                                          message:
                                              "Please Choose Correct Location",
                                          backgroundColor: AppTheme.red,
                                          isError: true);
                                    }
                                  } else {
                                    if (controller.projectNameController.value
                                            .text.isNotEmpty &&
                                        controller
                                            .projectNameController.value.text
                                            .trim()
                                            .isEmpty) {
                                      CommonFunction.showCustomSnackbar(
                                          message: "Space not allowed",
                                          isError: true,
                                          backgroundColor: Color(0xFFEF3B41));
                                    } else {
                                      CommonFunction.showCustomSnackbar(
                                          message:
                                              "Visibility, name are required.",
                                          isError: true,
                                          backgroundColor: Color(0xFFEF3B41));
                                    }
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        Space.height(30),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Obx(
            () => controller.isLoading.value &&
                    controller.arg != null &&
                    controller.arg["isEdit"] != null
                ? Container(
                    color: AppTheme.black.withValues(alpha: 0.5),
                    child: Loader(),
                  )
                : SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
